{"hash": "5cbf7973", "configHash": "a6bd2f14", "lockfileHash": "db7b5239", "browserHash": "f74dd367", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "64831835", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "0ad1f698", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "80014c44", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2e652501", "needsInterop": true}, "@tauri-apps/api/core": {"src": "../../@tauri-apps/api/core.js", "file": "@tauri-apps_api_core.js", "fileHash": "fcb6ec77", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "91d1ce72", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b05cfd45", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "144f89da", "needsInterop": true}, "react-youtube": {"src": "../../react-youtube/dist/YouTube.esm.js", "file": "react-youtube.js", "fileHash": "86a83727", "needsInterop": false}}, "chunks": {"chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}