{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[791294565694001944, "build_script_build", false, 15788650802416814541], [10755362358622467486, "build_script_build", false, 928340100157294331], [13890802266741835355, "build_script_build", false, 15572588462843427182], [15441187897486245138, "build_script_build", false, 15541235411082234131], [3935545708480822364, "build_script_build", false, 5199620768936266001], [1582828171158827377, "build_script_build", false, 11645940155553038951]], "local": [{"RerunIfChanged": {"output": "debug\\build\\lightning-shuffler-4ac1c977932a26d6\\output", "paths": ["tauri.conf.json", "capabilities", "youtube-fetcher.js"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}