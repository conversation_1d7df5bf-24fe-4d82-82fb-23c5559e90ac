#!/usr/bin/env node

const { search, getPlaylist } = require('youtube-sr');

async function fetchPlaylistData(playlistId) {
    try {
        // Fetch playlist data using youtube-sr
        const playlist = await getPlaylist(`https://www.youtube.com/playlist?list=${playlistId}`);
        
        if (!playlist) {
            throw new Error('Playlist not found');
        }

        // Extract videos from the playlist
        const videos = playlist.videos.map(video => ({
            id: video.id,
            title: video.title || 'Unknown Title',
            author: video.channel?.name || 'Unknown Channel',
            duration: formatDuration(video.duration),
            thumbnail: video.thumbnail?.url || `https://img.youtube.com/vi/${video.id}/mqdefault.jpg`
        }));

        // Prepare the response
        const response = {
            title: playlist.title || `Playlist ${playlistId.substring(0, 8)}`,
            author: playlist.channel?.name || 'Unknown',
            thumbnail: playlist.thumbnail?.url || (videos.length > 0 ? videos[0].thumbnail : ''),
            videos: videos
        };

        console.log(JSON.stringify(response));
    } catch (error) {
        console.log(JSON.stringify({
            error: `Failed to fetch playlist: ${error.message}`
        }));
        process.exit(1);
    }
}

function formatDuration(duration) {
    if (!duration) return '0:00';
    
    // If duration is already a string, return it
    if (typeof duration === 'string') return duration;
    
    // If duration is in milliseconds, convert to seconds
    if (typeof duration === 'number') {
        const totalSeconds = Math.floor(duration / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    return '0:00';
}

// Get playlist ID from command line arguments
const playlistId = process.argv[2];

if (!playlistId) {
    console.log(JSON.stringify({
        error: 'Playlist ID is required'
    }));
    process.exit(1);
}

// Fetch the playlist data
fetchPlaylistData(playlistId);
