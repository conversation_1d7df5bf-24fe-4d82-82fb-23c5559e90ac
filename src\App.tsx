import React, { useState, useEffect, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { motion, AnimatePresence } from 'framer-motion';
import YouTube from 'react-youtube';
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Shuffle,
  Repeat,
  Search,
  Plus,
  Trash2,
  RefreshCw,
  Volume2,
  Zap,
  HelpCircle,
  X
} from 'lucide-react';
import './App.css';

// Types
interface Video {
  id: string;
  title: string;
  author: string;
  thumbnail: string;
  duration: string;
}

interface Playlist {
  id: string;
  title: string;
  author: string;
  thumbnail: string;
  url: string;
  videos: Video[];
  video_count: number;
  created_at: string;
}

interface Mix {
  id: string;
  name: string;
  playlist_ids: string[];
  created_at: string;
}

interface AppData {
  playlists: Record<string, Playlist>;
  mixes: Record<string, Mix>;
  current_queue: Video[];
  current_index: number;
  shuffle_enabled: boolean;
  loop_count: number;
  volume: number;
}

function App() {
  // State
  const [appData, setAppData] = useState<AppData | null>(null);
  const [currentVideo, setCurrentVideo] = useState<Video | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [loopCount, setLoopCount] = useState(0);
  const [volume, setVolume] = useState(50);
  const [filteredVideos, setFilteredVideos] = useState<Video[]>([]);
  const [notification, setNotification] = useState<{ message: string, type: 'success' | 'error' } | null>(null);

  const playerRef = useRef<any>(null);

  // Load app data on mount
  useEffect(() => {
    loadAppData();
  }, []);

  // Setup system media controls
  useEffect(() => {
    if ('mediaSession' in navigator) {
      navigator.mediaSession.setActionHandler('play', () => {
        if (!isPlaying) togglePlayPause();
      });

      navigator.mediaSession.setActionHandler('pause', () => {
        if (isPlaying) togglePlayPause();
      });

      navigator.mediaSession.setActionHandler('previoustrack', () => {
        playPrevious();
      });

      navigator.mediaSession.setActionHandler('nexttrack', () => {
        playNext();
      });

      navigator.mediaSession.setActionHandler('seekbackward', () => {
        seekBackward();
      });

      navigator.mediaSession.setActionHandler('seekforward', () => {
        seekForward();
      });
    }
  }, [isPlaying]);

  // Update media session metadata when video changes
  useEffect(() => {
    if ('mediaSession' in navigator && currentVideo) {
      navigator.mediaSession.metadata = new MediaMetadata({
        title: currentVideo.title,
        artist: currentVideo.author,
        artwork: [
          {
            src: currentVideo.thumbnail,
            sizes: '320x180',
            type: 'image/jpeg'
          }
        ]
      });

      // Update tray tooltip
      invoke('update_tray_tooltip', {
        tooltip: `${isPlaying ? '▶️' : '⏸️'} ${currentVideo.title}`
      });
    }
  }, [currentVideo, isPlaying]);

  // Listen for tray events
  useEffect(() => {
    const unlisten1 = listen('tray-play-pause', () => {
      togglePlayPause();
    });

    const unlisten2 = listen('tray-next', () => {
      playNext();
    });

    const unlisten3 = listen('tray-previous', () => {
      playPrevious();
    });

    return () => {
      unlisten1.then(f => f());
      unlisten2.then(f => f());
      unlisten3.then(f => f());
    };
  }, []);

  // Filter videos based on search query
  useEffect(() => {
    if (!appData) return;

    const allVideos = appData.current_queue;
    if (!searchQuery.trim()) {
      setFilteredVideos(allVideos);
      return;
    }

    const filtered = allVideos.filter(video =>
      video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      video.author.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredVideos(filtered);
  }, [searchQuery, appData]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return;

      switch (e.key.toLowerCase()) {
        case ' ':
        case 'k':
          e.preventDefault();
          togglePlayPause();
          break;
        case 'arrowleft':
          e.preventDefault();
          seekBackward();
          break;
        case 'arrowright':
          e.preventDefault();
          seekForward();
          break;
        case 'm':
          e.preventDefault();
          toggleMute();
          break;
        case 'h':
          e.preventDefault();
          setShowHelp(true);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const loadAppData = async () => {
    try {
      const data = await invoke<AppData>('get_app_data');
      setAppData(data);
      if (data.current_queue.length > 0 && data.current_index < data.current_queue.length) {
        setCurrentVideo(data.current_queue[data.current_index]);
      }
      setLoopCount(data.loop_count);
      setVolume(data.volume || 50);
    } catch (error) {
      console.error('Failed to load app data:', error);
    }
  };

  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  const fetchYouTubePlaylist = async (playlistId: string): Promise<Playlist> => {
    try {
      // Use youtube-sr package to fetch playlist data
      const { search, getPlaylist } = await import('youtube-sr');

      const playlist = await getPlaylist(`https://www.youtube.com/playlist?list=${playlistId}`);

      if (!playlist) {
        throw new Error('Playlist not found');
      }

      // Extract videos from the playlist
      const videos: Video[] = playlist.videos.map((video: any) => ({
        id: video.id,
        title: video.title || 'Unknown Title',
        author: video.channel?.name || 'Unknown Channel',
        duration: formatDuration(video.duration),
        thumbnail: video.thumbnail?.url || `https://img.youtube.com/vi/${video.id}/mqdefault.jpg`
      }));

      return {
        id: playlistId,
        title: playlist.title || `Playlist ${playlistId.substring(0, 8)}`,
        author: playlist.channel?.name || 'Unknown',
        thumbnail: playlist.thumbnail?.url || (videos.length > 0 ? videos[0].thumbnail : ''),
        url: `https://www.youtube.com/playlist?list=${playlistId}`,
        videos: videos,
        video_count: videos.length,
        created_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('YouTube fetch error:', error);
      throw new Error(`Failed to fetch playlist: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formatDuration = (duration: any): string => {
    if (!duration) return '0:00';

    // If duration is already a string, return it
    if (typeof duration === 'string') return duration;

    // If duration is in milliseconds, convert to seconds
    if (typeof duration === 'number') {
      const totalSeconds = Math.floor(duration / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    return '0:00';
  };

  const addPlaylist = async () => {
    if (!playlistUrl.trim()) {
      showNotification('Please enter a playlist URL', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const playlistId = await invoke<string>('extract_playlist_id', { url: playlistUrl });

      // Fetch playlist data using the frontend YouTube API
      const playlist = await fetchYouTubePlaylist(playlistId);

      await invoke('add_playlist', { playlist });
      await loadAppData();
      setPlaylistUrl('');
      showNotification(`Added "${playlist.title}" successfully!`, 'success');
    } catch (error) {
      console.error('Failed to add playlist:', error);
      showNotification('Failed to add playlist. Please check the URL and try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const removePlaylist = async (playlistId: string) => {
    if (!confirm('Are you sure you want to delete this playlist?')) return;

    try {
      await invoke('remove_playlist', { playlistId });
      await loadAppData();
    } catch (error) {
      console.error('Failed to remove playlist:', error);
    }
  };

  const playPlaylist = async (playlist: Playlist) => {
    try {
      await invoke('set_current_queue', {
        videos: playlist.videos,
        shuffle: appData?.shuffle_enabled || false
      });
      await loadAppData();
      setCurrentVideo(playlist.videos[0]);
      setIsPlaying(true);
    } catch (error) {
      console.error('Failed to play playlist:', error);
    }
  };

  const togglePlayPause = () => {
    if (playerRef.current) {
      if (isPlaying) {
        playerRef.current.pauseVideo();
      } else {
        playerRef.current.playVideo();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const playNext = () => {
    if (!appData || appData.current_queue.length === 0) return;

    const nextIndex = (appData.current_index + 1) % appData.current_queue.length;
    setCurrentVideo(appData.current_queue[nextIndex]);
    // Update backend state would go here
  };

  const playPrevious = () => {
    if (!appData || appData.current_queue.length === 0) return;

    const prevIndex = appData.current_index === 0
      ? appData.current_queue.length - 1
      : appData.current_index - 1;
    setCurrentVideo(appData.current_queue[prevIndex]);
    // Update backend state would go here
  };

  const shuffleQueue = async () => {
    if (!appData) return;

    try {
      await invoke('set_current_queue', {
        videos: appData.current_queue,
        shuffle: true
      });
      await loadAppData();
    } catch (error) {
      console.error('Failed to shuffle queue:', error);
    }
  };

  const seekForward = () => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(currentTime + 10);
    }
  };

  const seekBackward = () => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(Math.max(0, currentTime - 10));
    }
  };

  const toggleMute = () => {
    if (playerRef.current) {
      if (playerRef.current.isMuted()) {
        playerRef.current.unMute();
      } else {
        playerRef.current.mute();
      }
    }
  };

  const handleLoopClick = (e: React.MouseEvent) => {
    if (e.shiftKey || e.button === 2) {
      setLoopCount(prev => prev + 1);
    } else {
      setLoopCount(prev => prev > 0 ? 0 : 1);
    }
  };

  const onPlayerReady = (event: any) => {
    playerRef.current = event.target;
    event.target.setVolume(volume);
  };

  const onPlayerStateChange = (event: any) => {
    const playerState = event.data;
    // YouTube player states: -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)
    const playing = playerState === 1;
    setIsPlaying(playing);

    // Update media session playback state
    if ('mediaSession' in navigator) {
      navigator.mediaSession.playbackState = playing ? 'playing' : 'paused';
    }

    if (playerState === 0) { // Video ended
      if (loopCount > 0) {
        setLoopCount(prev => prev - 1);
        event.target.playVideo();
      } else {
        playNext();
      }
    }
  };

  if (!appData) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#0a0a0a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '4px solid #00ff00',
            borderTop: '4px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ fontSize: '18px' }}>Loading Lightning Shuffler...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: '#0a0a0a',
      color: 'white',
      display: 'flex',
      fontFamily: 'Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
    }}>
      {/* Sidebar */}
      <div style={{
        width: '320px',
        background: '#1a1a1a',
        borderRight: '1px solid #374151',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Search Bar */}
        <div style={{ padding: '16px', borderBottom: '1px solid #374151' }}>
          <div style={{ position: 'relative' }}>
            <Search style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9CA3AF',
              width: '16px',
              height: '16px'
            }} />
            <input
              type="text"
              placeholder="Search videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: 'calc(100% - 8px)',
                paddingLeft: '40px',
                paddingRight: '16px',
                paddingTop: '8px',
                paddingBottom: '8px',
                background: '#2a2a2a',
                border: '1px solid #4B5563',
                borderRadius: '8px',
                color: 'white',
                outline: 'none',
                boxSizing: 'border-box'
              }}
            />
          </div>
        </div>

        {/* Playlists Section */}
        <div style={{ padding: '16px', borderBottom: '1px solid #374151' }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '12px',
            display: 'flex',
            alignItems: 'center'
          }}>
            Playlists
          </h3>

          {Object.values(appData.playlists).length === 0 ? (
            <p style={{ fontSize: '14px', color: '#9CA3AF', textAlign: 'center', padding: '16px 0' }}>
              No playlists added yet
            </p>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {Object.values(appData.playlists).map((playlist, index) => (
                <div
                  key={playlist.id}
                  className="slide-in-left"
                  style={{
                    padding: '8px',
                    borderRadius: '6px',
                    background: '#2a2a2a',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    animationDelay: `${index * 0.1}s`
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.background = '#374151'}
                  onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
                  onClick={() => playPlaylist(playlist)}
                >
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <p style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      margin: 0,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {playlist.title}
                    </p>
                    <p style={{
                      fontSize: '12px',
                      color: '#9CA3AF',
                      margin: 0
                    }}>
                      {playlist.video_count} videos
                    </p>
                  </div>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Refresh playlist functionality would go here
                      }}
                      style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        color: '#9CA3AF',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = '#00ff00'}
                      onMouseLeave={(e) => e.currentTarget.style.color = '#9CA3AF'}
                    >
                      <RefreshCw style={{ width: '14px', height: '14px' }} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removePlaylist(playlist.id);
                      }}
                      style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        color: '#9CA3AF',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = '#ff4444'}
                      onMouseLeave={(e) => e.currentTarget.style.color = '#9CA3AF'}
                    >
                      <Trash2 style={{ width: '14px', height: '14px' }} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Queue */}
        <div style={{ flex: 1, overflowY: 'auto' }}>
          <div style={{ padding: '16px' }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '16px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <Zap style={{
                width: '20px',
                height: '20px',
                marginRight: '8px',
                color: '#00ff00'
              }} />
              Current Queue
            </h3>

            {filteredVideos.map((video, index) => (
              <div
                key={video.id}
                className="fade-in"
                style={{
                  padding: '12px',
                  borderRadius: '8px',
                  marginBottom: '8px',
                  cursor: 'pointer',
                  background: currentVideo?.id === video.id
                    ? 'rgba(0, 255, 0, 0.2)'
                    : '#2a2a2a',
                  border: currentVideo?.id === video.id
                    ? '1px solid #00ff00'
                    : '1px solid transparent',
                  transition: 'all 0.2s',
                  animationDelay: `${index * 0.05}s`
                }}
                onClick={() => setCurrentVideo(video)}
                onMouseEnter={(e) => {
                  if (currentVideo?.id !== video.id) {
                    e.currentTarget.style.background = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentVideo?.id !== video.id) {
                    e.currentTarget.style.background = '#2a2a2a';
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    style={{
                      width: '48px',
                      height: '36px',
                      borderRadius: '4px',
                      objectFit: 'cover'
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <p style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      margin: 0
                    }}>
                      {video.title}
                    </p>
                    <p style={{
                      fontSize: '12px',
                      color: '#9CA3AF',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      margin: 0
                    }}>
                      {video.author}
                    </p>
                  </div>
                  <span style={{ fontSize: '12px', color: '#9CA3AF' }}>
                    {video.duration}
                  </span>
                </div>
              </div>
            ))}

            {filteredVideos.length === 0 && searchQuery && (
              <div style={{ textAlign: 'center', padding: '32px 0', color: '#9CA3AF' }}>
                <p>No results found</p>
                <p style={{ fontSize: '14px', marginTop: '4px' }}>
                  Check your spelling or try different keywords
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <img
                src="/Lightning_Bolt.png"
                alt="Lightning Shuffler"
                style={{ width: '32px', height: '32px' }}
              />
              <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
                Lightning Shuffler
              </h1>
            </div>

            <button
              onClick={() => setShowHelp(true)}
              style={{
                padding: '8px',
                borderRadius: '8px',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <HelpCircle style={{ width: '20px', height: '20px' }} />
            </button>
          </div>

          {/* Add Playlist */}
          <div style={{ marginTop: '16px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <div style={{ display: 'flex', gap: '8px' }}>
              <input
                type="text"
                placeholder="Enter YouTube playlist URL..."
                value={playlistUrl}
                onChange={(e) => setPlaylistUrl(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addPlaylist();
                  }
                }}
                style={{
                  flex: 1,
                  padding: '8px 16px',
                  background: '#2a2a2a',
                  border: '1px solid #4B5563',
                  borderRadius: '8px',
                  color: 'white',
                  outline: 'none'
                }}
              />
              <button
                onClick={addPlaylist}
                disabled={isLoading}
                style={{
                  padding: '8px 24px',
                  background: 'linear-gradient(45deg, #00ff00, #00cc00)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'black',
                  fontWeight: '600',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  opacity: isLoading ? 0.5 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'transform 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {isLoading ? (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid black',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                ) : (
                  <Plus style={{ width: '16px', height: '16px' }} />
                )}
                <span>Add Playlist</span>
              </button>
            </div>

            {/* Quick Test Button */}
            <button
              onClick={() => {
                setPlaylistUrl('https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9H1QNvg-ULY8ykX');
                setTimeout(() => addPlaylist(), 100);
              }}
              style={{
                padding: '6px 12px',
                background: 'transparent',
                border: '1px solid #4B5563',
                borderRadius: '6px',
                color: '#9CA3AF',
                fontSize: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#00ff00';
                e.currentTarget.style.color = '#00ff00';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#4B5563';
                e.currentTarget.style.color = '#9CA3AF';
              }}
            >
              ⚡ Try Sample Playlist
            </button>
          </div>
        </div>

        {/* Video Player */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0
        }}>
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '24px',
            paddingBottom: '12px'
          }}>
            {currentVideo ? (
              <div style={{ width: '100%', maxWidth: '900px' }}>
                <div style={{
                  position: 'relative',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  background: 'linear-gradient(45deg, rgba(0, 255, 0, 0.1) 0%, rgba(0, 255, 0, 0.05) 50%, rgba(0, 255, 0, 0.1) 100%)',
                  padding: '4px'
                }}>
                  <div style={{ borderRadius: '8px', overflow: 'hidden' }}>
                    <YouTube
                      videoId={currentVideo.id}
                      opts={{
                        width: '100%',
                        height: '400',
                        playerVars: {
                          autoplay: 1,
                          controls: 0,
                          modestbranding: 1,
                          rel: 0,
                        },
                      }}
                      onReady={onPlayerReady}
                      onStateChange={onPlayerStateChange}
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div style={{ textAlign: 'center' }}>
                <Zap style={{
                  width: '64px',
                  height: '64px',
                  margin: '0 auto 16px',
                  color: '#00ff00',
                  animation: 'lightning 2s ease-in-out infinite'
                }} />
                <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '8px' }}>
                  Welcome to Lightning Shuffler
                </h2>
                <p style={{ color: '#9CA3AF' }}>Add a playlist to get started</p>
              </div>
            )}
          </div>

          {/* Video Info - Fixed position above controls */}
          {currentVideo && (
            <div style={{
              textAlign: 'center',
              padding: '0 24px 12px',
              borderBottom: '1px solid #374151'
            }}>
              <h2 style={{
                fontSize: '18px',
                fontWeight: '600',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {currentVideo.title}
              </h2>
              <p style={{
                color: '#9CA3AF',
                margin: '4px 0 0 0',
                fontSize: '14px'
              }}>
                {currentVideo.author}
              </p>
            </div>
          )}
        </div>

        {/* Controls */}
        <div style={{ padding: '24px', borderTop: '1px solid #374151' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '24px'
          }}>
            <button
              onClick={playPrevious}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <SkipBack style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={togglePlayPause}
              style={{
                padding: '16px',
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #00ff00, #00cc00)',
                border: 'none',
                color: 'black',
                cursor: 'pointer',
                transition: 'transform 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              {isPlaying ?
                <Pause style={{ width: '32px', height: '32px' }} /> :
                <Play style={{ width: '32px', height: '32px' }} />
              }
            </button>

            <button
              onClick={playNext}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <SkipForward style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={shuffleQueue}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <Shuffle style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={handleLoopClick}
              onContextMenu={(e) => {
                e.preventDefault();
                handleLoopClick(e);
              }}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s',
                position: 'relative'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <Repeat style={{ width: '24px', height: '24px' }} />
              {loopCount > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-4px',
                  right: '-4px',
                  background: '#00ff00',
                  color: 'black',
                  fontSize: '12px',
                  borderRadius: '50%',
                  width: '20px',
                  height: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold'
                }}>
                  {loopCount}
                </span>
              )}
            </button>

            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Volume2 style={{ width: '20px', height: '20px' }} />
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  if (playerRef.current) {
                    playerRef.current.setVolume(newVolume);
                  }
                }}
                style={{
                  width: '80px',
                  accentColor: '#00ff00'
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Help Modal */}
      {showHelp && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 50
          }}
          onClick={() => setShowHelp(false)}
        >
          <div
            style={{
              background: 'rgba(26, 26, 26, 0.9)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              padding: '24px',
              borderRadius: '12px',
              maxWidth: '400px',
              width: '100%',
              margin: '16px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '16px'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', margin: 0 }}>
                Keyboard Shortcuts
              </h3>
              <button
                onClick={() => setShowHelp(false)}
                style={{
                  padding: '4px',
                  borderRadius: '4px',
                  background: 'transparent',
                  border: 'none',
                  color: 'white',
                  cursor: 'pointer',
                  transition: 'background 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
                onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              >
                <X style={{ width: '20px', height: '20px' }} />
              </button>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', fontSize: '14px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Play/Pause</span>
                <span style={{ color: '#00ff00' }}>Space or K</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Seek Forward 10s</span>
                <span style={{ color: '#00ff00' }}>Right Arrow</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Seek Backward 10s</span>
                <span style={{ color: '#00ff00' }}>Left Arrow</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Mute/Unmute</span>
                <span style={{ color: '#00ff00' }}>M</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Show Help</span>
                <span style={{ color: '#00ff00' }}>H</span>
              </div>
              <div style={{
                borderTop: '1px solid #4B5563',
                paddingTop: '12px',
                marginTop: '12px'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>Loop Toggle</span>
                  <span style={{ color: '#00ff00' }}>Click Loop Button</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>Loop Count +1</span>
                  <span style={{ color: '#00ff00' }}>Shift+Click or Right-Click</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            background: notification.type === 'success' ? '#00ff00' : '#ff4444',
            color: notification.type === 'success' ? '#000' : '#fff',
            fontWeight: '600',
            zIndex: 1000,
            animation: 'fadeIn 0.3s ease-out'
          }}
        >
          {notification.message}
        </div>
      )}
    </div>
  );
}

export default App;
